<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Custom Button Styles -->
    
    <!-- Primary Button Style -->
    <style name="CustomButton.Primary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">6dp</item>
        <item name="strokeWidth">2dp</item>
        <item name="strokeColor">@color/orange_primary</item>
        <item name="android:textColor">@color/orange_primary</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">@color/orange_primary_light</item>
    </style>
    
    <!-- Primary Button Filled Style -->
    <style name="CustomButton.Primary.Filled" parent="Widget.Material3.Button">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">6dp</item>
        <item name="backgroundTint">@color/orange_primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="rippleColor">@color/orange_primary_light</item>
    </style>
    
    <!-- Secondary Button Style -->
    <style name="CustomButton.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">6dp</item>
        <item name="strokeWidth">2dp</item>
        <item name="strokeColor">@color/gray_light</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">@color/gray_medium</item>
    </style>
    
    <!-- Success Button Style -->
    <style name="CustomButton.Success" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">6dp</item>
        <item name="strokeWidth">2dp</item>
        <item name="strokeColor">@color/success_green</item>
        <item name="android:textColor">@color/success_green</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">@color/success_green</item>
    </style>
    
    <!-- Error Button Style -->
    <style name="CustomButton.Error" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">6dp</item>
        <item name="strokeWidth">2dp</item>
        <item name="strokeColor">@color/error_red</item>
        <item name="android:textColor">@color/error_red</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">@color/error_red</item>
    </style>
    
    <!-- Text Button Style -->
    <style name="CustomButton.Text" parent="Widget.Material3.Button.TextButton">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">@color/orange_primary</item>
        <item name="rippleColor">@color/orange_primary_light</item>
    </style>
    
    <!-- Small Button Style -->
    <style name="CustomButton.Small" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">36dp</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">4dp</item>
        <item name="strokeWidth">1dp</item>
        <item name="strokeColor">@color/orange_primary</item>
        <item name="android:textColor">@color/orange_primary</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">@color/orange_primary_light</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
    </style>
</resources>
